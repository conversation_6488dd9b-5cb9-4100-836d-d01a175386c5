#!/bin/bash

# Battery One Logging Strategy Testing Script
# Tests the build-variant-based logging implementation
# Verifies logs appear in debug builds and are stripped from release builds

echo "=== Battery One Logging Strategy Testing ==="
echo "Testing build-variant-based logging implementation"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# App package name
PACKAGE_NAME="com.fc.p.tj.charginganimation.batterycharging.chargeeffect"

# Function to display colored output
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_with_timestamp() {
    echo -e "${BLUE}[$(date '+%H:%M:%S')] $1${NC}"
}

# Check if we're in the right directory
if [ ! -f "app/build.gradle.kts" ]; then
    log_error "This script must be run from the project root directory"
    exit 1
fi

# Check if ADB is available
if ! command -v adb &> /dev/null; then
    log_error "ADB is not available. Please install Android SDK platform-tools"
    exit 1
fi

# Check if device is connected and select one if multiple
DEVICE_COUNT=$(adb devices | grep -c "device$")
if [ $DEVICE_COUNT -eq 0 ]; then
    log_error "No Android device connected. Please connect a device or start an emulator"
    exit 1
elif [ $DEVICE_COUNT -gt 1 ]; then
    log_warning "Multiple devices detected. Selecting emulator for testing..."
    # Try to use emulator first, fallback to first device
    SELECTED_DEVICE=$(adb devices | grep "emulator" | head -1 | awk '{print $1}')
    if [ -z "$SELECTED_DEVICE" ]; then
        SELECTED_DEVICE=$(adb devices | grep "device$" | head -1 | awk '{print $1}')
    fi
    ADB_DEVICE_FLAG="-s $SELECTED_DEVICE"
    log_info "Selected device: $SELECTED_DEVICE"
else
    ADB_DEVICE_FLAG=""
    log_info "Single device detected"
fi

log_info "Starting logging strategy testing..."

echo ""
echo "=== TEST PLAN ==="
echo "1. 🔨 Build debug APK and verify logging is enabled"
echo "2. 📱 Install and test debug build with ADB logcat monitoring"
echo "3. 🔨 Build release APK and verify logging is stripped"
echo "4. 📱 Install and test release build with ADB logcat monitoring"
echo "5. 📊 Compare results and generate report"
echo ""

read -p "Continue with testing? (y/n): " continue_test

if [ "$continue_test" != "y" ] && [ "$continue_test" != "Y" ]; then
    log_info "Testing cancelled"
    exit 0
fi

# Test results tracking
DEBUG_BUILD_SUCCESS=false
DEBUG_LOGS_PRESENT=false
RELEASE_BUILD_SUCCESS=false
RELEASE_LOGS_ABSENT=false

echo ""
echo "=== STEP 1: DEBUG BUILD TESTING ==="
log_with_timestamp "Building debug APK..."

if ./gradlew clean assembleDebug; then
    log_success "Debug build completed successfully"
    DEBUG_BUILD_SUCCESS=true
else
    log_error "Debug build failed"
    exit 1
fi

# Check if debug APK exists
DEBUG_APK="app/build/outputs/apk/debug/app-debug.apk"
if [ -f "$DEBUG_APK" ]; then
    log_success "Debug APK found: $DEBUG_APK"
else
    log_error "Debug APK not found"
    exit 1
fi

echo ""
echo "=== STEP 2: DEBUG BUILD LOG TESTING ==="
log_with_timestamp "Installing debug APK..."

if adb $ADB_DEVICE_FLAG install -r "$DEBUG_APK"; then
    log_success "Debug APK installed successfully"
else
    log_error "Failed to install debug APK"
    exit 1
fi

log_with_timestamp "Starting debug log monitoring..."

# Clear existing logs
adb $ADB_DEVICE_FLAG logcat -c

# Start the app
log_info "Launching app..."
adb $ADB_DEVICE_FLAG shell am start -n "$PACKAGE_NAME/.activity.splash.SplashActivity"
sleep 3

# Monitor logs for 10 seconds
log_info "Monitoring logs for 10 seconds..."
timeout 10 adb $ADB_DEVICE_FLAG logcat | grep -E "(BatteryApplication|CoreBatteryStatsService|BatteryLogger)" > debug_logs.txt &
LOGCAT_PID=$!

# Wait for monitoring to complete
wait $LOGCAT_PID 2>/dev/null

# Check if logs were captured
if [ -s debug_logs.txt ]; then
    DEBUG_LOGS_PRESENT=true
    log_success "Debug logs captured successfully"
    echo "Sample debug logs:"
    head -5 debug_logs.txt | while read line; do
        echo "  📝 $line"
    done
    echo "  ... (see debug_logs.txt for full output)"
else
    log_warning "No debug logs captured - this might indicate an issue"
fi

echo ""
echo "=== STEP 3: RELEASE BUILD TESTING ==="
log_with_timestamp "Building release APK..."

if ./gradlew assembleRelease; then
    log_success "Release build completed successfully"
    RELEASE_BUILD_SUCCESS=true
else
    log_error "Release build failed"
    exit 1
fi

# Check if release APK exists
RELEASE_APK="app/build/outputs/apk/release/app-release.apk"
if [ -f "$RELEASE_APK" ]; then
    log_success "Release APK found: $RELEASE_APK"
else
    log_error "Release APK not found"
    exit 1
fi

echo ""
echo "=== STEP 4: RELEASE BUILD LOG TESTING ==="
log_with_timestamp "Installing release APK..."

if adb $ADB_DEVICE_FLAG install -r "$RELEASE_APK"; then
    log_success "Release APK installed successfully"
else
    log_error "Failed to install release APK"
    exit 1
fi

log_with_timestamp "Starting release log monitoring..."

# Clear existing logs
adb $ADB_DEVICE_FLAG logcat -c

# Start the app
log_info "Launching app..."
adb $ADB_DEVICE_FLAG shell am start -n "$PACKAGE_NAME/.activity.splash.SplashActivity"
sleep 3

# Monitor logs for 10 seconds
log_info "Monitoring logs for 10 seconds..."
timeout 10 adb $ADB_DEVICE_FLAG logcat | grep -E "(BatteryApplication|CoreBatteryStatsService|BatteryLogger)" > release_logs.txt &
LOGCAT_PID=$!

# Wait for monitoring to complete
wait $LOGCAT_PID 2>/dev/null

# Check if logs were captured (should be empty for release)
if [ -s release_logs.txt ]; then
    log_warning "Release logs found - logging may not be properly stripped"
    echo "Unexpected release logs:"
    cat release_logs.txt | while read line; do
        echo "  ⚠️  $line"
    done
else
    RELEASE_LOGS_ABSENT=true
    log_success "No release logs found - logging properly stripped!"
fi

echo ""
echo "=== STEP 5: RESULTS AND REPORT ==="

# Generate test report
REPORT_FILE="logging_test_report_$(date +%Y%m%d_%H%M%S).txt"

cat > "$REPORT_FILE" << EOF
Battery One Logging Strategy Test Report
Generated: $(date)
========================================

Test Configuration:
- Package: $PACKAGE_NAME
- Debug APK: $DEBUG_APK
- Release APK: $RELEASE_APK

Test Results:
=============

1. Debug Build:
   - Build Success: $DEBUG_BUILD_SUCCESS
   - Logs Present: $DEBUG_LOGS_PRESENT
   - Status: $([ "$DEBUG_BUILD_SUCCESS" = true ] && [ "$DEBUG_LOGS_PRESENT" = true ] && echo "✅ PASS" || echo "❌ FAIL")

2. Release Build:
   - Build Success: $RELEASE_BUILD_SUCCESS
   - Logs Absent: $RELEASE_LOGS_ABSENT
   - Status: $([ "$RELEASE_BUILD_SUCCESS" = true ] && [ "$RELEASE_LOGS_ABSENT" = true ] && echo "✅ PASS" || echo "❌ FAIL")

Overall Status:
===============
$([ "$DEBUG_BUILD_SUCCESS" = true ] && [ "$DEBUG_LOGS_PRESENT" = true ] && [ "$RELEASE_BUILD_SUCCESS" = true ] && [ "$RELEASE_LOGS_ABSENT" = true ] && echo "✅ ALL TESTS PASSED" || echo "❌ SOME TESTS FAILED")

Debug Logs Sample:
==================
EOF

if [ -f debug_logs.txt ]; then
    echo "" >> "$REPORT_FILE"
    head -10 debug_logs.txt >> "$REPORT_FILE"
fi

cat >> "$REPORT_FILE" << EOF

Release Logs (should be empty):
===============================
EOF

if [ -f release_logs.txt ]; then
    echo "" >> "$REPORT_FILE"
    cat release_logs.txt >> "$REPORT_FILE"
else
    echo "(No logs found - CORRECT)" >> "$REPORT_FILE"
fi

# Display summary
echo ""
echo "=== TEST SUMMARY ==="
echo "📊 Debug Build: $([ "$DEBUG_BUILD_SUCCESS" = true ] && echo "✅ SUCCESS" || echo "❌ FAILED")"
echo "📊 Debug Logs: $([ "$DEBUG_LOGS_PRESENT" = true ] && echo "✅ PRESENT" || echo "❌ MISSING")"
echo "📊 Release Build: $([ "$RELEASE_BUILD_SUCCESS" = true ] && echo "✅ SUCCESS" || echo "❌ FAILED")"
echo "📊 Release Logs: $([ "$RELEASE_LOGS_ABSENT" = true ] && echo "✅ STRIPPED" || echo "❌ PRESENT")"

echo ""
if [ "$DEBUG_BUILD_SUCCESS" = true ] && [ "$DEBUG_LOGS_PRESENT" = true ] && [ "$RELEASE_BUILD_SUCCESS" = true ] && [ "$RELEASE_LOGS_ABSENT" = true ]; then
    log_success "🎉 ALL TESTS PASSED! Logging strategy is working correctly."
    echo ""
    echo "✅ Debug builds show logs as expected"
    echo "✅ Release builds have logs stripped as expected"
    echo "✅ Build-variant-based logging is functioning properly"
else
    log_error "❌ SOME TESTS FAILED! Please review the issues:"
    
    if [ "$DEBUG_BUILD_SUCCESS" != true ]; then
        echo "   - Debug build failed"
    fi
    if [ "$DEBUG_LOGS_PRESENT" != true ]; then
        echo "   - Debug logs not present (check BatteryLogger implementation)"
    fi
    if [ "$RELEASE_BUILD_SUCCESS" != true ]; then
        echo "   - Release build failed"
    fi
    if [ "$RELEASE_LOGS_ABSENT" != true ]; then
        echo "   - Release logs not stripped (check ProGuard configuration)"
    fi
fi

echo ""
log_info "📄 Detailed report saved to: $REPORT_FILE"
log_info "📄 Debug logs saved to: debug_logs.txt"
log_info "📄 Release logs saved to: release_logs.txt"

echo ""
echo "=== NEXT STEPS ==="
echo "1. 📖 Review the detailed report: cat $REPORT_FILE"
echo "2. 🔄 If tests failed, check the implementation and ProGuard rules"
echo "3. 🚀 If tests passed, proceed with full migration using migrate_logging.sh"
echo "4. 🧪 Test the migrated code with your specific use cases"

# Cleanup
log_info "Cleaning up temporary files..."
# Keep log files for review, but clean up any other temp files if needed

log_success "Testing completed!"
