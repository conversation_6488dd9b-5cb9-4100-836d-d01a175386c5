#!/bin/bash

# Localization Verification Script
# Verifies that all background permission dialog strings are present in all locale files

echo "=== Background Permission Dialog Localization Verification ==="
echo ""

# Define the required string keys
REQUIRED_STRINGS=(
    "background_permission_dialog_title"
    "background_permission_dialog_message"
    "background_permission_allow"
    "background_permission_close"
    "background_permission_dont_kill_link"
)

# Define all locale directories
LOCALE_DIRS=(
    "values"
    "values-ar"
    "values-de"
    "values-es"
    "values-fr"
    "values-hu"
    "values-it"
    "values-nl"
    "values-pl"
    "values-pt"
    "values-ro"
    "values-ru"
    "values-tr"
    "values-uk"
    "values-zh"
)

# Counters
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0

echo "Checking ${#REQUIRED_STRINGS[@]} required strings across ${#LOCALE_DIRS[@]} locales..."
echo ""

# Check each locale
for locale in "${LOCALE_DIRS[@]}"; do
    locale_path="app/src/main/res/$locale/strings.xml"
    
    if [ ! -f "$locale_path" ]; then
        echo "❌ MISSING FILE: $locale_path"
        continue
    fi
    
    echo "📁 Checking locale: $locale"
    locale_missing=0
    
    # Check each required string
    for string_key in "${REQUIRED_STRINGS[@]}"; do
        TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
        
        if grep -q "name=\"$string_key\"" "$locale_path"; then
            echo "  ✅ $string_key"
            PASSED_CHECKS=$((PASSED_CHECKS + 1))
        else
            echo "  ❌ MISSING: $string_key"
            FAILED_CHECKS=$((FAILED_CHECKS + 1))
            locale_missing=1
        fi
    done
    
    if [ $locale_missing -eq 0 ]; then
        echo "  🎉 All strings present in $locale"
    else
        echo "  ⚠️  Missing strings in $locale"
    fi
    
    echo ""
done

# Summary
echo "=== VERIFICATION SUMMARY ==="
echo "Total checks performed: $TOTAL_CHECKS"
echo "Passed checks: $PASSED_CHECKS"
echo "Failed checks: $FAILED_CHECKS"
echo ""

if [ $FAILED_CHECKS -eq 0 ]; then
    echo "🎉 SUCCESS: All background permission dialog strings are present in all locales!"
    echo "✅ Localization is complete and ready for production."
else
    echo "❌ FAILURE: $FAILED_CHECKS missing string(s) found."
    echo "⚠️  Please add the missing strings before proceeding."
fi

echo ""
echo "=== ADDITIONAL CHECKS ==="

# Check for XML syntax errors
echo "🔍 Checking XML syntax..."
xml_errors=0

for locale in "${LOCALE_DIRS[@]}"; do
    locale_path="app/src/main/res/$locale/strings.xml"
    
    if [ -f "$locale_path" ]; then
        if ! xmllint --noout "$locale_path" 2>/dev/null; then
            echo "❌ XML syntax error in $locale_path"
            xml_errors=$((xml_errors + 1))
        fi
    fi
done

if [ $xml_errors -eq 0 ]; then
    echo "✅ All XML files have valid syntax"
else
    echo "❌ $xml_errors XML syntax error(s) found"
fi

echo ""
echo "=== VERIFICATION COMPLETE ==="
