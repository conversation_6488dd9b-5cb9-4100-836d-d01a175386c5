#!/bin/bash

# Background Permission Dialog Testing Script
# Tests the background permission dialog system implementation
# Bundle ID: com.fc.p.tj.charginganimation.batterycharging.chargeeffect

PACKAGE_NAME="com.fc.p.tj.charginganimation.batterycharging.chargeeffect"
LOG_TAG="BackgroundPermission|BackgroundPermissionDialog|MainActivity"

echo "=== Background Permission Dialog Testing Script ==="
echo "Package: $PACKAGE_NAME"
echo "Testing comprehensive background permission dialog functionality"
echo ""

# Function to check if device is connected
check_device() {
    if ! adb devices | grep -q "device$"; then
        echo "❌ No Android device connected. Please connect a device and enable USB debugging."
        exit 1
    fi
    echo "✅ Android device connected"
}

# Function to install and launch app
install_and_launch() {
    echo ""
    echo "📱 Installing and launching app..."
    
    # Install APK (assuming it's built)
    if [ -f "app/build/outputs/apk/debug/app-debug.apk" ]; then
        adb install -r app/build/outputs/apk/debug/app-debug.apk
        echo "✅ App installed successfully"
    else
        echo "⚠️  APK not found, assuming app is already installed"
    fi
    
    # Launch app
    adb shell am start -n "$PACKAGE_NAME/.activity.main.MainActivity"
    echo "✅ App launched"
    sleep 3
}

# Function to monitor logs with filtering
monitor_logs() {
    echo ""
    echo "📋 Monitoring background permission logs..."
    echo "Press Ctrl+C to stop monitoring"
    echo ""
    
    adb logcat -c  # Clear existing logs
    adb logcat | grep -E "$LOG_TAG" --line-buffered
}

# Function to test permission states
test_permission_states() {
    echo ""
    echo "🔧 Testing different permission states..."
    
    echo ""
    echo "1. Testing with battery optimization ENABLED (dialog should show):"
    echo "   Enabling battery optimization for the app..."
    
    # Enable battery optimization (this will make the dialog show)
    adb shell cmd appops set $PACKAGE_NAME RUN_IN_BACKGROUND ignore
    adb shell dumpsys deviceidle whitelist -$PACKAGE_NAME 2>/dev/null || true
    
    echo "   Restarting app to trigger dialog..."
    adb shell am force-stop $PACKAGE_NAME
    sleep 2
    adb shell am start -n "$PACKAGE_NAME/.activity.main.MainActivity"
    
    echo "   ✅ App restarted - dialog should appear if permission not granted"
    echo "   📋 Check device screen for background permission dialog"
    echo "   📋 Monitor logs below for dialog events"
    
    sleep 5
    
    echo ""
    echo "2. Testing with battery optimization DISABLED (dialog should NOT show):"
    echo "   Disabling battery optimization for the app..."
    
    # Disable battery optimization (this will prevent dialog from showing)
    adb shell cmd appops set $PACKAGE_NAME RUN_IN_BACKGROUND allow
    adb shell dumpsys deviceidle whitelist +$PACKAGE_NAME 2>/dev/null || true
    
    echo "   Restarting app..."
    adb shell am force-stop $PACKAGE_NAME
    sleep 2
    adb shell am start -n "$PACKAGE_NAME/.activity.main.MainActivity"
    
    echo "   ✅ App restarted - dialog should NOT appear"
    echo "   📋 Monitor logs below to confirm no dialog events"
    
    sleep 5
}

# Function to test dialog interactions
test_dialog_interactions() {
    echo ""
    echo "🖱️  Testing dialog interactions..."

    # First ensure dialog will show
    adb shell cmd appops set $PACKAGE_NAME RUN_IN_BACKGROUND ignore
    adb shell dumpsys deviceidle whitelist -$PACKAGE_NAME 2>/dev/null || true

    echo "   Restarting app to show dialog..."
    adb shell am force-stop $PACKAGE_NAME
    sleep 2
    adb shell am start -n "$PACKAGE_NAME/.activity.main.MainActivity"

    echo ""
    echo "   📋 Manual testing required:"
    echo "   1. Verify dialog appears with correct content"
    echo "   2. Test 'Don't Kill My App' link opens browser"
    echo "   3. Test 'Close' button dismisses dialog"
    echo "   4. Test 'Allow' button opens permission settings"
    echo "   5. Grant permission and verify dialog auto-closes"
    echo "   6. Verify dialog doesn't show on next launch when permission granted"

    sleep 10
}

# Function to test rate limiting and cooldown behavior
test_rate_limiting() {
    echo ""
    echo "⏱️  Testing rate limiting and cooldown behavior..."

    # Ensure dialog will show initially
    adb shell cmd appops set $PACKAGE_NAME RUN_IN_BACKGROUND ignore
    adb shell dumpsys deviceidle whitelist -$PACKAGE_NAME 2>/dev/null || true

    echo ""
    echo "1. Testing initial dialog display:"
    adb shell am force-stop $PACKAGE_NAME
    sleep 2
    adb shell am start -n "$PACKAGE_NAME/.activity.main.MainActivity"
    echo "   ✅ App started - dialog should appear"
    echo "   📋 Please click 'Close' button to test cooldown"
    sleep 10

    echo ""
    echo "2. Testing cooldown period (dialog should NOT appear):"
    adb shell am force-stop $PACKAGE_NAME
    sleep 2
    adb shell am start -n "$PACKAGE_NAME/.activity.main.MainActivity"
    echo "   ✅ App restarted - dialog should NOT appear due to cooldown"
    sleep 5

    echo ""
    echo "3. Testing app resume during cooldown:"
    adb shell input keyevent KEYCODE_HOME
    sleep 2
    adb shell am start -n "$PACKAGE_NAME/.activity.main.MainActivity"
    echo "   ✅ App resumed - dialog should still NOT appear"
    sleep 5

    echo ""
    echo "4. Testing permission grant resets cooldown:"
    echo "   📋 Manually grant battery optimization permission in device settings"
    echo "   📋 Then revoke it to test cooldown reset"
    sleep 15

    adb shell cmd appops set $PACKAGE_NAME RUN_IN_BACKGROUND ignore
    adb shell am force-stop $PACKAGE_NAME
    sleep 2
    adb shell am start -n "$PACKAGE_NAME/.activity.main.MainActivity"
    echo "   ✅ App restarted after permission revocation - dialog should appear (cooldown reset)"
    sleep 5
}

# Function to test auto-close behavior
test_auto_close() {
    echo ""
    echo "🔄 Testing auto-close behavior..."

    # Ensure dialog will show
    adb shell cmd appops set $PACKAGE_NAME RUN_IN_BACKGROUND ignore
    adb shell dumpsys deviceidle whitelist -$PACKAGE_NAME 2>/dev/null || true

    echo "   Restarting app to show dialog..."
    adb shell am force-stop $PACKAGE_NAME
    sleep 2
    adb shell am start -n "$PACKAGE_NAME/.activity.main.MainActivity"

    echo ""
    echo "   📋 Auto-close testing steps:"
    echo "   1. Verify dialog appears"
    echo "   2. Click 'Allow' button"
    echo "   3. Grant permission in settings"
    echo "   4. Return to app and verify dialog auto-closes"
    echo "   5. Check logs for auto-close events"

    sleep 15
}

# Function to test app lifecycle scenarios
test_lifecycle_scenarios() {
    echo ""
    echo "🔄 Testing app lifecycle scenarios..."
    
    # Ensure dialog will show
    adb shell cmd appops set $PACKAGE_NAME RUN_IN_BACKGROUND ignore
    
    echo ""
    echo "1. Testing onCreate scenario:"
    adb shell am force-stop $PACKAGE_NAME
    sleep 2
    adb shell am start -n "$PACKAGE_NAME/.activity.main.MainActivity"
    echo "   ✅ App started fresh - check for dialog"
    sleep 3
    
    echo ""
    echo "2. Testing onResume scenario:"
    adb shell input keyevent KEYCODE_HOME  # Send app to background
    sleep 2
    adb shell am start -n "$PACKAGE_NAME/.activity.main.MainActivity"  # Bring back to foreground
    echo "   ✅ App resumed - check for dialog"
    sleep 3
    
    echo ""
    echo "3. Testing multiple resume cycles:"
    for i in {1..3}; do
        echo "   Resume cycle $i:"
        adb shell input keyevent KEYCODE_HOME
        sleep 1
        adb shell am start -n "$PACKAGE_NAME/.activity.main.MainActivity"
        sleep 2
    done
    echo "   ✅ Multiple resume cycles completed"
}

# Function to check current permission status
check_permission_status() {
    echo ""
    echo "🔍 Checking current permission status..."
    
    echo "Battery optimization status:"
    adb shell dumpsys deviceidle | grep -i "$PACKAGE_NAME" || echo "   Not in whitelist (optimization enabled)"
    
    echo ""
    echo "App ops status:"
    adb shell cmd appops get $PACKAGE_NAME RUN_IN_BACKGROUND || echo "   Could not retrieve app ops status"
    
    echo ""
    echo "Power manager status:"
    adb shell dumpsys power | grep -A 5 -B 5 "$PACKAGE_NAME" || echo "   No power manager entries found"
}

# Function to run comprehensive test suite
run_comprehensive_tests() {
    echo ""
    echo "🧪 Running comprehensive test suite..."

    check_device
    install_and_launch
    check_permission_status
    test_permission_states
    test_dialog_interactions
    test_rate_limiting
    test_auto_close
    test_lifecycle_scenarios

    echo ""
    echo "✅ Comprehensive testing completed!"
    echo ""
    echo "📋 Final verification checklist:"
    echo "   □ Dialog appears when permission not granted"
    echo "   □ Dialog does not appear when permission granted"
    echo "   □ 'Don't Kill My App' link opens browser correctly"
    echo "   □ 'Close' button dismisses dialog and starts cooldown"
    echo "   □ 'Allow' button opens permission settings"
    echo "   □ Dialog auto-closes when permission is granted"
    echo "   □ 30-minute cooldown period works correctly"
    echo "   □ Permission grant resets cooldown timer"
    echo "   □ Cooldown persists across app restarts"
    echo "   □ Dialog content matches onboarding slide design"
    echo "   □ No crashes or memory leaks observed"
    echo "   □ Proper logging appears in filtered logcat"
    echo ""
}

# Main execution
case "${1:-comprehensive}" in
    "logs")
        check_device
        monitor_logs
        ;;
    "permission")
        check_device
        test_permission_states
        monitor_logs
        ;;
    "interaction")
        check_device
        test_dialog_interactions
        monitor_logs
        ;;
    "rate-limiting")
        check_device
        test_rate_limiting
        monitor_logs
        ;;
    "auto-close")
        check_device
        test_auto_close
        monitor_logs
        ;;
    "lifecycle")
        check_device
        test_lifecycle_scenarios
        monitor_logs
        ;;
    "status")
        check_device
        check_permission_status
        ;;
    "comprehensive"|*)
        run_comprehensive_tests
        echo ""
        echo "Starting log monitoring (Press Ctrl+C to stop):"
        monitor_logs
        ;;
esac
