#!/bin/bash

# Battery One Logging Migration Script
# Migrates existing Log.* calls to BatteryLogger.* calls
# This script provides a semi-automated migration with safety checks

echo "=== Battery One Logging Migration Script ==="
echo "This script will help migrate existing Log.* calls to BatteryLogger.* calls"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to display colored output
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if we're in the right directory
if [ ! -f "app/build.gradle.kts" ]; then
    log_error "This script must be run from the project root directory"
    exit 1
fi

log_info "Analyzing current logging usage in the codebase..."

# Find all Kotlin files with logging statements
KOTLIN_FILES=$(find app/src/main/java -name "*.kt" -type f)
TOTAL_FILES=0
FILES_WITH_LOGGING=0
TOTAL_LOG_STATEMENTS=0

echo ""
echo "=== LOGGING ANALYSIS ==="

for file in $KOTLIN_FILES; do
    TOTAL_FILES=$((TOTAL_FILES + 1))
    
    # Count different types of log statements
    LOG_D_COUNT=$(grep -c "Log\.d(" "$file" 2>/dev/null || echo 0)
    LOG_I_COUNT=$(grep -c "Log\.i(" "$file" 2>/dev/null || echo 0)
    LOG_W_COUNT=$(grep -c "Log\.w(" "$file" 2>/dev/null || echo 0)
    LOG_E_COUNT=$(grep -c "Log\.e(" "$file" 2>/dev/null || echo 0)
    LOG_V_COUNT=$(grep -c "Log\.v(" "$file" 2>/dev/null || echo 0)
    ANDROID_LOG_COUNT=$(grep -c "android\.util\.Log\." "$file" 2>/dev/null || echo 0)
    PRINTLN_COUNT=$(grep -c "println(" "$file" 2>/dev/null || echo 0)
    
    FILE_TOTAL=$((LOG_D_COUNT + LOG_I_COUNT + LOG_W_COUNT + LOG_E_COUNT + LOG_V_COUNT + ANDROID_LOG_COUNT + PRINTLN_COUNT))
    
    if [ $FILE_TOTAL -gt 0 ]; then
        FILES_WITH_LOGGING=$((FILES_WITH_LOGGING + 1))
        TOTAL_LOG_STATEMENTS=$((TOTAL_LOG_STATEMENTS + FILE_TOTAL))
        
        echo "📄 $(basename "$file"): $FILE_TOTAL logging statements"
        if [ $LOG_D_COUNT -gt 0 ]; then echo "   - Log.d: $LOG_D_COUNT"; fi
        if [ $LOG_I_COUNT -gt 0 ]; then echo "   - Log.i: $LOG_I_COUNT"; fi
        if [ $LOG_W_COUNT -gt 0 ]; then echo "   - Log.w: $LOG_W_COUNT"; fi
        if [ $LOG_E_COUNT -gt 0 ]; then echo "   - Log.e: $LOG_E_COUNT"; fi
        if [ $LOG_V_COUNT -gt 0 ]; then echo "   - Log.v: $LOG_V_COUNT"; fi
        if [ $ANDROID_LOG_COUNT -gt 0 ]; then echo "   - android.util.Log: $ANDROID_LOG_COUNT"; fi
        if [ $PRINTLN_COUNT -gt 0 ]; then echo "   - println: $PRINTLN_COUNT"; fi
    fi
done

echo ""
echo "=== SUMMARY ==="
echo "📊 Total Kotlin files: $TOTAL_FILES"
echo "📊 Files with logging: $FILES_WITH_LOGGING"
echo "📊 Total logging statements: $TOTAL_LOG_STATEMENTS"

if [ $TOTAL_LOG_STATEMENTS -eq 0 ]; then
    log_success "No logging statements found to migrate!"
    exit 0
fi

echo ""
echo "=== MIGRATION OPTIONS ==="
echo "1. 🔄 Automatic migration (recommended for most cases)"
echo "2. 📝 Generate migration report only"
echo "3. 🧪 Test migration on a single file"
echo "4. ❌ Exit without changes"
echo ""

read -p "Choose an option (1-4): " choice

case $choice in
    1)
        log_info "Starting automatic migration..."
        
        # Create backup directory
        BACKUP_DIR="logging_migration_backup_$(date +%Y%m%d_%H%M%S)"
        mkdir -p "$BACKUP_DIR"
        
        log_info "Creating backup in $BACKUP_DIR..."
        
        # Backup files with logging
        for file in $KOTLIN_FILES; do
            FILE_TOTAL=$(grep -c -E "(Log\.[diwev]|android\.util\.Log\.|println\()" "$file" 2>/dev/null || echo 0)
            if [ $FILE_TOTAL -gt 0 ]; then
                cp "$file" "$BACKUP_DIR/"
            fi
        done
        
        log_success "Backup created successfully"
        
        # Perform migration
        MIGRATED_FILES=0
        
        for file in $KOTLIN_FILES; do
            FILE_TOTAL=$(grep -c -E "(Log\.[diwev]|android\.util\.Log\.|println\()" "$file" 2>/dev/null || echo 0)
            if [ $FILE_TOTAL -gt 0 ]; then
                log_info "Migrating $(basename "$file")..."
                
                # Add BatteryLogger import if not present
                if ! grep -q "import com.tqhit.battery.one.utils.BatteryLogger" "$file"; then
                    # Find the last import line and add BatteryLogger import after it
                    sed -i '/^import /a import com.tqhit.battery.one.utils.BatteryLogger' "$file"
                fi
                
                # Replace Log.* calls with BatteryLogger.* calls
                sed -i 's/Log\.d(/BatteryLogger.d(/g' "$file"
                sed -i 's/Log\.i(/BatteryLogger.i(/g' "$file"
                sed -i 's/Log\.w(/BatteryLogger.w(/g' "$file"
                sed -i 's/Log\.e(/BatteryLogger.e(/g' "$file"
                sed -i 's/Log\.v(/BatteryLogger.v(/g' "$file"
                
                # Replace android.util.Log calls
                sed -i 's/android\.util\.Log\.d(/BatteryLogger.d(/g' "$file"
                sed -i 's/android\.util\.Log\.i(/BatteryLogger.i(/g' "$file"
                sed -i 's/android\.util\.Log\.w(/BatteryLogger.w(/g' "$file"
                sed -i 's/android\.util\.Log\.e(/BatteryLogger.e(/g' "$file"
                sed -i 's/android\.util\.Log\.v(/BatteryLogger.v(/g' "$file"
                
                MIGRATED_FILES=$((MIGRATED_FILES + 1))
            fi
        done
        
        log_success "Migration completed!"
        log_success "Migrated $MIGRATED_FILES files"
        log_warning "Backup saved in: $BACKUP_DIR"
        log_info "Please review the changes and test your application"
        ;;
        
    2)
        log_info "Generating migration report..."
        
        REPORT_FILE="logging_migration_report_$(date +%Y%m%d_%H%M%S).txt"
        
        echo "Battery One Logging Migration Report" > "$REPORT_FILE"
        echo "Generated: $(date)" >> "$REPORT_FILE"
        echo "========================================" >> "$REPORT_FILE"
        echo "" >> "$REPORT_FILE"
        echo "Summary:" >> "$REPORT_FILE"
        echo "- Total Kotlin files: $TOTAL_FILES" >> "$REPORT_FILE"
        echo "- Files with logging: $FILES_WITH_LOGGING" >> "$REPORT_FILE"
        echo "- Total logging statements: $TOTAL_LOG_STATEMENTS" >> "$REPORT_FILE"
        echo "" >> "$REPORT_FILE"
        echo "Files requiring migration:" >> "$REPORT_FILE"
        echo "=========================" >> "$REPORT_FILE"
        
        for file in $KOTLIN_FILES; do
            FILE_TOTAL=$(grep -c -E "(Log\.[diwev]|android\.util\.Log\.|println\()" "$file" 2>/dev/null || echo 0)
            if [ $FILE_TOTAL -gt 0 ]; then
                echo "" >> "$REPORT_FILE"
                echo "File: $file" >> "$REPORT_FILE"
                echo "Logging statements: $FILE_TOTAL" >> "$REPORT_FILE"
                
                # Show actual logging lines
                grep -n -E "(Log\.[diwev]|android\.util\.Log\.|println\()" "$file" >> "$REPORT_FILE" 2>/dev/null || true
            fi
        done
        
        log_success "Report generated: $REPORT_FILE"
        ;;
        
    3)
        log_info "Test migration mode - select a file to test:"
        
        # Show files with logging
        FILES_ARRAY=()
        INDEX=1
        
        for file in $KOTLIN_FILES; do
            FILE_TOTAL=$(grep -c -E "(Log\.[diwev]|android\.util\.Log\.|println\()" "$file" 2>/dev/null || echo 0)
            if [ $FILE_TOTAL -gt 0 ]; then
                echo "$INDEX. $(basename "$file") ($FILE_TOTAL statements)"
                FILES_ARRAY+=("$file")
                INDEX=$((INDEX + 1))
            fi
        done
        
        echo ""
        read -p "Enter file number to test: " file_choice
        
        if [ "$file_choice" -ge 1 ] && [ "$file_choice" -le "${#FILES_ARRAY[@]}" ]; then
            SELECTED_FILE="${FILES_ARRAY[$((file_choice - 1))]}"
            
            log_info "Testing migration on: $(basename "$SELECTED_FILE")"
            
            # Create test file
            TEST_FILE="${SELECTED_FILE}.test"
            cp "$SELECTED_FILE" "$TEST_FILE"
            
            # Perform test migration
            if ! grep -q "import com.tqhit.battery.one.utils.BatteryLogger" "$TEST_FILE"; then
                sed -i '/^import /a import com.tqhit.battery.one.utils.BatteryLogger' "$TEST_FILE"
            fi
            
            sed -i 's/Log\.d(/BatteryLogger.d(/g' "$TEST_FILE"
            sed -i 's/Log\.i(/BatteryLogger.i(/g' "$TEST_FILE"
            sed -i 's/Log\.w(/BatteryLogger.w(/g' "$TEST_FILE"
            sed -i 's/Log\.e(/BatteryLogger.e(/g' "$TEST_FILE"
            sed -i 's/Log\.v(/BatteryLogger.v(/g' "$TEST_FILE"
            
            log_success "Test migration completed"
            log_info "Original file: $SELECTED_FILE"
            log_info "Test file: $TEST_FILE"
            log_info "Review the test file and delete it when done"
        else
            log_error "Invalid file selection"
        fi
        ;;
        
    4)
        log_info "Exiting without changes"
        exit 0
        ;;
        
    *)
        log_error "Invalid option"
        exit 1
        ;;
esac

echo ""
echo "=== NEXT STEPS ==="
echo "1. 🔨 Build the project: ./gradlew assembleDebug"
echo "2. 🧪 Test debug build with ADB: adb logcat | grep 'BatteryApplication\\|CoreBatteryStatsService'"
echo "3. 🚀 Build release: ./gradlew assembleRelease"
echo "4. ✅ Verify no logs in release: adb logcat (should show no app logs)"
echo ""
log_success "Migration process completed!"
